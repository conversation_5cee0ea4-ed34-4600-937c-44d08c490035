<!-- Filters -->
<div class="exam-card p-6 mb-6">
    <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
        <div class="flex flex-col sm:flex-row gap-4">
            <!-- Trade Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Trade:</label>
                <select id="tradeFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.trade === 'all' ? 'selected' : '' %>>All Trades</option>
                    <% trades.forEach(trade => { %>
                        <option value="<%= trade.original_trade %>" <%= filters.trade === trade.original_trade ? 'selected' : '' %>><%= trade.trade_name %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Class Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Class:</label>
                <select id="classFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.class === 'all' ? 'selected' : '' %>>All Classes</option>
                    <% classes.forEach(classItem => { %>
                        <option value="<%= classItem.class %>" <%= filters.class === classItem.class ? 'selected' : '' %>>Class <%= classItem.class %></option>
                    <% }); %>
                </select>
            </div>

            <!-- Section Filter -->
            <div class="flex items-center">
                <label class="text-sm font-medium text-gray-700 mr-2">Section:</label>
                <select id="sectionFilter" class="border border-gray-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="all" <%= filters.section === 'all' ? 'selected' : '' %>>All Sections</option>
                    <% sections.forEach(section => { %>
                        <option value="<%= section.section %>" <%= filters.section === section.section ? 'selected' : '' %>>Section <%= section.section %></option>
                    <% }); %>
                </select>
            </div>
        </div>

        <div class="flex gap-2">
            <button onclick="applyFilters()" class="btn-exam-primary">
                <i class="fas fa-filter mr-2"></i>Apply Filters
            </button>
            <% if (isCustomRanges) { %>
                <div class="bg-green-100 text-green-800 px-3 py-2 rounded-lg flex items-center text-sm">
                    <i class="fas fa-check-circle mr-2"></i>
                    Custom Ranges Applied
                </div>
                <button onclick="resetToDefaultRanges()" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center">
                    <i class="fas fa-undo mr-2"></i>
                    Reset to Default
                </button>
            <% } %>
            <button onclick="customizeRanges()" class="btn-exam-secondary">
                <i class="fas fa-cog mr-2"></i>                Customize Ranges
            </button>
        </div>
    </div>
</div>

<!-- Distribution Summary -->
<div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-gray-600 text-sm font-medium">Total Students</p>
                <p class="text-3xl font-bold text-gray-800"><%= totalStudents %></p>
                <p class="text-blue-600 text-sm mt-1">
                    <i class="fas fa-users mr-1"></i>
                    In Analysis
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-graduation-cap"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <% const excellentCount = distribution.find(d => d.min >= 90)?.count || 0; %>
                <p class="text-gray-600 text-sm font-medium">Excellent (90%+)</p>
                <p class="text-3xl font-bold text-green-600"><%= excellentCount %></p>
                <p class="text-green-600 text-sm mt-1">
                    <i class="fas fa-star mr-1"></i>
                    <%= totalStudents > 0 ? ((excellentCount / totalStudents) * 100).toFixed(1) : 0 %>%
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-trophy"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <% const passCount = distribution.filter(d => d.min >= 33).reduce((sum, d) => sum + d.count, 0); %>
                <p class="text-gray-600 text-sm font-medium">Passed (33%+)</p>
                <p class="text-3xl font-bold text-blue-600"><%= passCount %></p>
                <p class="text-blue-600 text-sm mt-1">
                    <i class="fas fa-check-circle mr-1"></i>
                    <%= totalStudents > 0 ? ((passCount / totalStudents) * 100).toFixed(1) : 0 %>%
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-user-check"></i>
            </div>
        </div>
    </div>

    <div class="stats-card">
        <div class="flex items-center justify-between">
            <div>
                <% const failCount = distribution.filter(d => d.max < 33).reduce((sum, d) => sum + d.count, 0); %>
                <p class="text-gray-600 text-sm font-medium">Need Support</p>
                <p class="text-3xl font-bold text-red-600"><%= failCount %></p>
                <p class="text-red-600 text-sm mt-1">
                    <i class="fas fa-exclamation-triangle mr-1"></i>
                    <%= totalStudents > 0 ? ((failCount / totalStudents) * 100).toFixed(1) : 0 %>%
                </p>
            </div>
            <div class="stats-icon">
                <i class="fas fa-user-times"></i>
            </div>
        </div>
    </div>
</div>

<!-- Distribution Chart and Table -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
    <!-- Distribution Chart -->
    <div class="exam-card p-6">
        <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center">
            <i class="fas fa-chart-pie text-purple-600 mr-2"></i>
            Score Distribution Chart
        </h2>
        <div class="h-64">
            <canvas id="distributionChart"></canvas>
        </div>
    </div>

    <!-- Distribution Table -->
    <div class="exam-card">
        <div class="p-6 border-b">
            <h2 class="text-xl font-bold text-gray-800 flex items-center">
                <i class="fas fa-table text-blue-600 mr-2"></i>
                Distribution Breakdown
            </h2>
        </div>
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-gradient-to-r from-blue-600 to-green-600 text-white">
                    <tr>
                        <th class="px-4 py-3 text-left">Score Range</th>
                        <th class="px-4 py-3 text-center">Students</th>
                        <th class="px-4 py-3 text-center">Percentage</th>
                        <th class="px-4 py-3 text-center">Visual</th>
                    </tr>
                </thead>
                <tbody>
                    <% distribution.forEach((range, index) => { %>
                        <tr class="<%= index % 2 === 0 ? 'bg-gray-50' : 'bg-white' %> hover:bg-blue-50 cursor-pointer" onclick="showRangeDetails(<%= index %>)">
                            <td class="px-4 py-3">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded mr-3" style="background: <%= getColorForRange(range.min) %>"></div>
                                    <div>
                                        <p class="font-semibold text-gray-800"><%= range.label %></p>
                                        <p class="text-xs text-gray-500"><%= range.min %>% - <%= range.max %>%</p>
                                    </div>
                                </div>
                            </td>
                            <td class="px-4 py-3 text-center">
                                <span class="font-bold text-lg text-gray-800"><%= range.count %></span>
                            </td>
                            <td class="px-4 py-3 text-center">
                                <span class="font-medium <%= range.percentage >= 30 ? 'text-green-600' : range.percentage >= 15 ? 'text-yellow-600' : 'text-red-600' %>">
                                    <%= range.percentage.toFixed(1) %>%
                                </span>
                            </td>
                            <td class="px-4 py-3">
                                <div class="w-full bg-gray-200 rounded-full h-3">
                                    <div class="h-3 rounded-full" style="width: <%= range.percentage %>%; background: <%= getColorForRange(range.min) %>"></div>
                                </div>
                            </td>
                        </tr>
                    <% }); %>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Detailed Student Lists -->
<div class="exam-card">
    <div class="p-6 border-b">
        <h2 class="text-xl font-bold text-gray-800 flex items-center">
            <i class="fas fa-list text-green-600 mr-2"></i>
            Student Details by Score Range
        </h2>
    </div>
    
    <div class="p-6">
        <div class="space-y-6">
            <% distribution.forEach((range, index) => { %>
                <% if (range.count > 0) { %>
                    <div class="border border-gray-200 rounded-lg">
                        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200 cursor-pointer" onclick="toggleRangeDetails(<%= index %>)">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center">
                                    <div class="w-4 h-4 rounded mr-3" style="background: <%= getColorForRange(range.min) %>"></div>
                                    <h3 class="font-semibold text-gray-800"><%= range.label %></h3>
                                    <span class="ml-2 px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                                        <%= range.count %> students
                                    </span>
                                </div>
                                <i class="fas fa-chevron-down text-gray-500" id="chevron-<%= index %>"></i>
                            </div>
                        </div>
                        <div id="range-details-<%= index %>" class="hidden p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                <% range.students.forEach(student => { %>
                                    <div class="bg-white border border-gray-200 rounded-lg p-3 hover:shadow-md transition-shadow">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="font-medium text-gray-800"><%= student.name %></p>
                                                <p class="text-sm text-gray-600"><%= student.roll_number %></p>
                                                <p class="text-xs text-gray-500"><%= student.class %>-<%= student.section %> | <%= student.trade %></p>
                                            </div>
                                            <div class="text-right">
                                                <p class="font-bold text-lg <%= (student.percentage || 0) >= 75 ? 'text-green-600' : (student.percentage || 0) >= 50 ? 'text-yellow-600' : 'text-red-600' %>">
                                                    <%= (student.percentage || 0).toFixed ? (student.percentage || 0).toFixed(1) : (student.percentage || 0) %>%
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                <% }); %>
                            </div>
                        </div>
                    </div>
                <% } %>
            <% }); %>
        </div>
    </div>
</div>

<!-- Custom Range Modal -->
<div id="customRangeModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full">
            <div class="p-6 border-b">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-gray-800">Customize Score Ranges</h2>
                    <button onclick="closeCustomRangeModal()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="customRangeInputs" class="space-y-4">
                    <!-- Dynamic range inputs will be added here -->
                </div>
                <div class="flex justify-between mt-6">
                    <button onclick="addCustomRange()" class="btn-exam-secondary">
                        <i class="fas fa-plus mr-2"></i>Add Range
                    </button>
                    <div class="space-x-2">
                        <button onclick="resetToDefault()" class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50">
                            Reset to Default
                        </button>
                        <button onclick="applyCustomRanges()" class="btn-exam-primary">
                            Apply Changes
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Pass distribution data to the external script and initialize
document.addEventListener('DOMContentLoaded', function() {
    // Store distribution data globally for use in external script
    <%
    let safeDistribution = [];
    try {
        safeDistribution = (distribution || []).map(d => ({
            min: d.min || 0,
            max: d.max || 100,
            label: d.label || 'Unknown Range',
            count: d.count || 0,
            percentage: d.percentage || 0,
            students: d.students || []
        }));
    } catch (e) {
        safeDistribution = [];
    }
    %>
    window.scoreDistributionData = <%= JSON.stringify(safeDistribution) %>;
    window.isCustomRanges = <%= isCustomRanges ? 'true' : 'false' %>;
    <% if (isCustomRanges && customRangesData) { %>
    window.customRangesData = <%= JSON.stringify(customRangesData) %>;
    <% } %>

    // Initialize the chart with the distribution data
    if (window.ScoreDistribution && window.ScoreDistribution.initializeDistributionChart) {
        window.ScoreDistribution.initializeDistributionChart(window.scoreDistributionData);
    }

    // Initialize score distribution functionality
    if (window.ScoreDistribution && window.ScoreDistribution.initializeScoreDistribution) {
        window.ScoreDistribution.initializeScoreDistribution();
    }
});
</script>

<%
function getColorForRange(min) {
    if (min >= 90) return 'linear-gradient(135deg, #10b981, #059669)';
    if (min >= 80) return 'linear-gradient(135deg, #3b82f6, #1d4ed8)';
    if (min >= 70) return 'linear-gradient(135deg, #8b5cf6, #7c3aed)';
    if (min >= 60) return 'linear-gradient(135deg, #06b6d4, #0891b2)';
    if (min >= 50) return 'linear-gradient(135deg, #f59e0b, #d97706)';
    if (min >= 40) return 'linear-gradient(135deg, #f97316, #ea580c)';
    return 'linear-gradient(135deg, #ef4444, #dc2626)';
}
%>
