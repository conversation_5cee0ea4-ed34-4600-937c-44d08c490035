/**
 * Test Script for Custom Ranges Database Functionality
 * This script tests the custom ranges database operations
 */

const mysql = require('mysql2/promise');
require('dotenv').config();

const dbConfig = {
    host: process.env.DB_HOST,
    user: process.env.DB_USER,
    password: process.env.DB_PASSWORD,
    database: process.env.DB_NAME,
    port: process.env.DB_PORT
};

async function testCustomRanges() {
    let connection;
    
    try {
        console.log('🧪 Testing Custom Ranges Database Functionality...\n');
        
        connection = await mysql.createConnection(dbConfig);
        
        // Test 1: Check if table exists and has default data
        console.log('📋 Test 1: Checking table structure and default data');
        const [tableCheck] = await connection.query(`
            SELECT COUNT(*) as count 
            FROM custom_score_ranges 
            WHERE academic_session = '2023-2024' AND created_by = 'system'
        `);
        console.log(`✅ Found ${tableCheck[0].count} default ranges for 2023-2024 session\n`);
        
        // Test 2: Insert custom ranges for testing
        console.log('📝 Test 2: Inserting custom ranges');
        
        // First deactivate existing ranges
        await connection.query(`
            UPDATE custom_score_ranges 
            SET is_active = FALSE 
            WHERE academic_session = '2023-2024'
        `);
        
        // Insert custom test ranges
        const customRanges = [
            { min: 95, max: 100, label: '95-100 (Outstanding)' },
            { min: 85, max: 94, label: '85-94 (Excellent)' },
            { min: 75, max: 84, label: '75-84 (Very Good)' },
            { min: 65, max: 74, label: '65-74 (Good)' },
            { min: 50, max: 64, label: '50-64 (Average)' },
            { min: 0, max: 49, label: 'Below 50 (Needs Improvement)' }
        ];
        
        for (let i = 0; i < customRanges.length; i++) {
            const range = customRanges[i];
            await connection.query(`
                INSERT INTO custom_score_ranges (
                    academic_session,
                    range_name,
                    min_percentage,
                    max_percentage,
                    range_label,
                    display_order,
                    is_active,
                    created_by
                ) VALUES (?, ?, ?, ?, ?, ?, TRUE, 'test')
            `, [
                '2023-2024',
                `test_range_${i + 1}`,
                range.min,
                range.max,
                range.label,
                i + 1
            ]);
        }
        
        console.log(`✅ Successfully inserted ${customRanges.length} custom test ranges\n`);
        
        // Test 3: Retrieve custom ranges
        console.log('📖 Test 3: Retrieving custom ranges');
        const [retrievedRanges] = await connection.query(`
            SELECT 
                range_name,
                min_percentage as min,
                max_percentage as max,
                range_label as label,
                display_order,
                created_by
            FROM custom_score_ranges
            WHERE academic_session = '2023-2024' AND is_active = TRUE
            ORDER BY display_order
        `);
        
        console.log('Retrieved ranges:');
        retrievedRanges.forEach((range, index) => {
            console.log(`  ${index + 1}. ${range.label} (${range.min}%-${range.max}%) - Created by: ${range.created_by}`);
        });
        console.log('');
        
        // Test 4: Check if custom ranges exist
        console.log('🔍 Test 4: Checking for custom ranges');
        const [customCheck] = await connection.query(`
            SELECT COUNT(*) as count
            FROM custom_score_ranges
            WHERE academic_session = '2023-2024' 
            AND is_active = TRUE 
            AND created_by != 'system'
        `);
        
        const hasCustomRanges = customCheck[0].count > 0;
        console.log(`✅ Has custom ranges: ${hasCustomRanges ? 'YES' : 'NO'} (${customCheck[0].count} custom ranges found)\n`);
        
        // Test 5: Reset to default ranges
        console.log('🔄 Test 5: Resetting to default ranges');
        
        // Deactivate custom ranges
        await connection.query(`
            UPDATE custom_score_ranges 
            SET is_active = FALSE 
            WHERE academic_session = '2023-2024' AND created_by = 'test'
        `);
        
        // Reactivate system default ranges
        await connection.query(`
            UPDATE custom_score_ranges 
            SET is_active = TRUE 
            WHERE academic_session = '2023-2024' AND created_by = 'system'
        `);
        
        console.log('✅ Successfully reset to default ranges\n');
        
        // Test 6: Verify reset
        console.log('✔️ Test 6: Verifying reset to defaults');
        const [defaultCheck] = await connection.query(`
            SELECT 
                range_label as label,
                created_by
            FROM custom_score_ranges
            WHERE academic_session = '2023-2024' AND is_active = TRUE
            ORDER BY display_order
        `);
        
        console.log('Active ranges after reset:');
        defaultCheck.forEach((range, index) => {
            console.log(`  ${index + 1}. ${range.label} - Created by: ${range.created_by}`);
        });
        console.log('');
        
        // Test 7: Clean up test data
        console.log('🧹 Test 7: Cleaning up test data');
        await connection.query(`
            DELETE FROM custom_score_ranges 
            WHERE academic_session = '2023-2024' AND created_by = 'test'
        `);
        console.log('✅ Test data cleaned up\n');
        
        console.log('🎉 All tests completed successfully!');
        console.log('\n📊 Summary:');
        console.log('✅ Table structure verified');
        console.log('✅ Custom ranges insertion works');
        console.log('✅ Custom ranges retrieval works');
        console.log('✅ Custom ranges detection works');
        console.log('✅ Reset to defaults works');
        console.log('✅ Data cleanup works');
        
    } catch (error) {
        console.error('❌ Test failed:', error);
        console.error('Error details:', {
            message: error.message,
            code: error.code,
            sqlState: error.sqlState
        });
    } finally {
        if (connection) {
            await connection.end();
            console.log('\n🔌 Database connection closed');
        }
    }
}

// Run the test
if (require.main === module) {
    testCustomRanges();
}

module.exports = { testCustomRanges };
